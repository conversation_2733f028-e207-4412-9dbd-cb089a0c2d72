/**
 * @file file_utils.h
 * @brief 文件操作工具模块
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#ifndef FILE_UTILS_H
#define FILE_UTILS_H

#include <stddef.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 配置文件类型枚举
 */
typedef enum {
    CONFIG_TYPE_BINARY,    // 二进制配置文件
    CONFIG_TYPE_INI        // INI格式配置文件
} config_type_e;

/**
 * @brief 读取配置文件
 * @param filepath 文件路径
 * @param type 文件类型
 * @param buffer 缓冲区
 * @param size 缓冲区大小
 * @return 0成功，-1失败
 */
int file_utils_read(const char *filepath, config_type_e type, 
                   void *buffer, size_t size);

/**
 * @brief 写入配置文件
 * @param filepath 文件路径
 * @param type 文件类型
 * @param buffer 数据缓冲区
 * @param size 数据大小
 * @return 0成功，-1失败
 */
int file_utils_write(const char *filepath, config_type_e type, 
                    const void *buffer, size_t size);

/**
 * @brief 备份配置文件
 * @param filepath 文件路径
 * @return 0成功，-1失败
 */
int file_utils_backup(const char *filepath);

/**
 * @brief 恢复配置文件
 * @param filepath 文件路径
 * @return 0成功，-1失败
 */
int file_utils_restore(const char *filepath);

/**
 * @brief 检查文件是否存在
 * @param filepath 文件路径
 * @return 1存在，0不存在
 */
int file_utils_exists(const char *filepath);

/**
 * @brief 获取文件大小
 * @param filepath 文件路径
 * @return 文件大小，-1失败
 */
long file_utils_size(const char *filepath);

/**
 * @brief 创建目录（递归）
 * @param dirpath 目录路径
 * @return 0成功，-1失败
 */
int file_utils_mkdir_recursive(const char *dirpath);

/**
 * @brief 读取二进制配置文件 - 兼容旧项目ReadBinCfg
 * @param filepath 文件路径
 * @param offset 偏移量
 * @param size 读取大小
 * @param buffer 缓冲区
 * @return 实际读取字节数，-1失败
 */
int file_utils_read_binary(const char *filepath, int offset, size_t size, void *buffer);

/**
 * @brief 写入二进制配置文件 - 兼容旧项目WriteBinCfg
 * @param filepath 文件路径
 * @param offset 偏移量
 * @param size 写入大小
 * @param buffer 数据缓冲区
 * @return 实际写入字节数，-1失败
 */
int file_utils_write_binary(const char *filepath, int offset, size_t size, const void *buffer);

/**
 * @brief 读取INI格式配置文件 - 兼容旧项目INI读取方式
 * @param filepath 文件路径
 * @param buffer 缓冲区
 * @param size 缓冲区大小
 * @return 实际读取字节数，-1失败
 */
int file_utils_read_ini(const char *filepath, void *buffer, size_t size);

/**
 * @brief 写入INI格式配置文件 - 兼容旧项目INI写入方式
 * @param filepath 文件路径
 * @param buffer 数据缓冲区
 * @param size 数据大小
 * @return 实际写入字节数，-1失败
 */
int file_utils_write_ini(const char *filepath, const void *buffer, size_t size);

#ifdef __cplusplus
}
#endif

#endif /* FILE_UTILS_H */
