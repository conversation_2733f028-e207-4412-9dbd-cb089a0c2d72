/**
 * @file center_config.h
 * @brief 呼叫中心配置管理模块
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#ifndef CENTER_CONFIG_H
#define CENTER_CONFIG_H

#include <stdint.h>
#include <cjson/cJSON.h>
#include <microhttpd.h>

// 呼叫中心配置结构体（按命名规范）
// 对应旧项目中的stCfgCenter结构
typedef struct {
    uint32_t server_ip;         // 服务器IP地址（二进制格式）
    uint16_t server_port;       // 服务器端口
    uint32_t local_ip;          // 本地IP地址
    uint16_t local_port;        // 本地端口
    uint8_t  enable;            // 启用标志
    uint8_t  reserved[3];       // 保留字段，保持结构体对齐
} cfg_center_t;

/**
 * @brief 初始化呼叫中心配置模块
 * @return 0成功，-1失败
 */
int center_config_init(void);

/**
 * @brief 清理呼叫中心配置模块
 */
void center_config_cleanup(void);

/**
 * @brief 获取呼叫中心配置信息（API处理函数）
 * @param connection HTTP连接
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小
 * @param con_cls 连接状态
 * @return 0成功，-1失败
 */
int handle_center_get(struct MHD_Connection *connection, 
                     const char *url, 
                     const char *method,
                     const char *upload_data, 
                     size_t *upload_data_size,
                     void **con_cls);

/**
 * @brief 设置呼叫中心配置信息（API处理函数）
 * @param connection HTTP连接
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小
 * @param con_cls 连接状态
 * @return 0成功，-1失败
 */
int handle_center_post(struct MHD_Connection *connection, 
                      const char *url, 
                      const char *method,
                      const char *upload_data, 
                      size_t *upload_data_size,
                      void **con_cls);

/**
 * @brief 加载呼叫中心配置
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int center_config_load(cfg_center_t *config);

/**
 * @brief 保存呼叫中心配置
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int center_config_save(const cfg_center_t *config);

/**
 * @brief 验证呼叫中心配置
 * @param config 配置结构指针
 * @return 0有效，-1无效
 */
int center_config_validate(const cfg_center_t *config);

/**
 * @brief 配置结构转JSON
 * @param config 配置结构指针
 * @param json JSON对象指针
 * @return 0成功，-1失败
 */
int center_config_to_json(const cfg_center_t *config, cJSON **json);

/**
 * @brief JSON转配置结构
 * @param json JSON对象
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int center_json_to_config(const cJSON *json, cfg_center_t *config);

/**
 * @brief 设置默认配置
 * @param config 配置结构指针
 */
void center_config_set_default(cfg_center_t *config);

// 配置文件路径（按照旧项目的路径）
#define CENTER_CONFIG_FILE "/opt/cfg/center.cfg"

// 默认配置值
#define CENTER_DEFAULT_SERVER_PORT  8080
#define CENTER_DEFAULT_LOCAL_PORT   8081

#endif // CENTER_CONFIG_H
