/**
 * @file network_utils.h
 * @brief 网络操作工具模块
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#ifndef NETWORK_UTILS_H
#define NETWORK_UTILS_H

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief IP字符串转二进制
 * @param ip_str IP字符串
 * @param ip_binary 二进制IP指针
 * @return 0成功，-1失败
 */
int ip_utils_string_to_binary(const char *ip_str, uint32_t *ip_binary);

/**
 * @brief IP二进制转字符串
 * @param ip_binary 二进制IP
 * @param ip_str 字符串缓冲区
 * @param size 缓冲区大小
 * @return 0成功，-1失败
 */
int ip_utils_binary_to_string(uint32_t ip_binary, char *ip_str, size_t size);

/**
 * @brief 验证IP地址格式
 * @param ip_str IP字符串
 * @return 0有效，-1无效
 */
int ip_utils_validate_address(const char *ip_str);

/**
 * @brief 验证子网掩码格式
 * @param mask_str 掩码字符串
 * @return 0有效，-1无效
 */
int ip_utils_validate_mask(const char *mask_str);

/**
 * @brief 验证MAC地址格式
 * @param mac_str MAC字符串
 * @return 0有效，-1无效
 */
int ip_utils_validate_mac(const char *mac_str);

/**
 * @brief 检查IP地址是否在同一网段
 * @param ip1 第一个IP地址
 * @param ip2 第二个IP地址
 * @param mask 子网掩码
 * @return 1在同一网段，0不在同一网段
 */
int ip_utils_same_subnet(uint32_t ip1, uint32_t ip2, uint32_t mask);

/**
 * @brief 获取网络地址
 * @param ip IP地址
 * @param mask 子网掩码
 * @return 网络地址
 */
uint32_t ip_utils_get_network(uint32_t ip, uint32_t mask);

/**
 * @brief 获取广播地址
 * @param ip IP地址
 * @param mask 子网掩码
 * @return 广播地址
 */
uint32_t ip_utils_get_broadcast(uint32_t ip, uint32_t mask);

/**
 * @brief MAC地址字符串转二进制
 * @param mac_str MAC字符串 (格式: XX:XX:XX:XX:XX:XX)
 * @param mac_binary 二进制MAC数组 (6字节)
 * @return 0成功，-1失败
 */
int mac_utils_string_to_binary(const char *mac_str, uint8_t mac_binary[6]);

/**
 * @brief MAC地址二进制转字符串
 * @param mac_binary 二进制MAC数组 (6字节)
 * @param mac_str 字符串缓冲区
 * @param size 缓冲区大小
 * @return 0成功，-1失败
 */
int mac_utils_binary_to_string(const uint8_t mac_binary[6], char *mac_str, size_t size);

#ifdef __cplusplus
}
#endif

#endif /* NETWORK_UTILS_H */
