/**
 * @file gateway_config.h
 * @brief 网关配置管理模块 - 对应旧项目0gateway.c
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#ifndef GATEWAY_CONFIG_H
#define GATEWAY_CONFIG_H

#include <stdint.h>
#include <cjson/cJSON.h>
#include <microhttpd.h>

// 网关配置结构体（按命名规范）
// 对应旧项目中的stCfgGateway结构
typedef struct {
    uint32_t gateway_ip;        // 网关IP地址（二进制格式）
    uint16_t gateway_port;      // 网关端口
    uint32_t local_ip;          // 本地IP地址
    uint16_t local_port;        // 本地端口
    uint8_t  enable;            // 启用标志
    uint8_t  protocol_type;     // 协议类型 (0=TCP, 1=UDP)
    uint16_t timeout;           // 超时时间（秒）
} cfg_gateway_t;

/**
 * @brief 初始化网关配置模块
 * @return 0成功，-1失败
 */
int gateway_config_init(void);

/**
 * @brief 清理网关配置模块
 */
void gateway_config_cleanup(void);

/**
 * @brief 获取网关配置信息（API处理函数）
 * @param connection HTTP连接
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小
 * @param con_cls 连接状态
 * @return 0成功，-1失败
 */
int handle_gateway_get(struct MHD_Connection *connection, 
                      const char *url, 
                      const char *method,
                      const char *upload_data, 
                      size_t *upload_data_size,
                      void **con_cls);

/**
 * @brief 设置网关配置信息（API处理函数）
 * @param connection HTTP连接
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小
 * @param con_cls 连接状态
 * @return 0成功，-1失败
 */
int handle_gateway_post(struct MHD_Connection *connection, 
                       const char *url, 
                       const char *method,
                       const char *upload_data, 
                       size_t *upload_data_size,
                       void **con_cls);

/**
 * @brief 加载网关配置
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int gateway_config_load(cfg_gateway_t *config);

/**
 * @brief 保存网关配置
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int gateway_config_save(const cfg_gateway_t *config);

/**
 * @brief 验证网关配置
 * @param config 配置结构指针
 * @return 0有效，-1无效
 */
int gateway_config_validate(const cfg_gateway_t *config);

/**
 * @brief 配置结构转JSON
 * @param config 配置结构指针
 * @param json JSON对象指针
 * @return 0成功，-1失败
 */
int gateway_config_to_json(const cfg_gateway_t *config, cJSON **json);

/**
 * @brief JSON转配置结构
 * @param json JSON对象
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int gateway_json_to_config(const cJSON *json, cfg_gateway_t *config);

/**
 * @brief 设置默认配置
 * @param config 配置结构指针
 */
void gateway_config_set_default(cfg_gateway_t *config);

// 配置文件路径（按照旧项目的路径）
#define GATEWAY_CONFIG_FILE "/opt/cfg/gateway.cfg"

// 默认配置值
#define GATEWAY_DEFAULT_PORT        8080
#define GATEWAY_DEFAULT_LOCAL_PORT  8081
#define GATEWAY_DEFAULT_TIMEOUT     30

#endif // GATEWAY_CONFIG_H
