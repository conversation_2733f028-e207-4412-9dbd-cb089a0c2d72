/**
 * @file main.c
 * @brief WebCfg模块化系统主程序 - 基于libmicrohttpd的HTTP服务器
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#include <stdio.h>
#include <stdlib.h>
#include <signal.h>
#include <unistd.h>
#include "config_interface.h"
#include "network_config.h"
#include "center_config.h"
#include "gateway_config.h"
#include "api_router.h"

// 全局变量
static volatile int g_server_running = 1;

/**
 * @brief 信号处理函数
 */
static void signal_handler(int sig) {
    (void)sig; // 避免未使用参数警告
    printf("\nReceived shutdown signal, stopping server...\n");
    g_server_running = 0;
}

/**
 * @brief 注册API路由 - 按照设计方案注册网络配置API
 */
static int register_api_routes(void) {
    // 注册网络配置API路由 - 按照设计方案的API接口
    if (api_router_register("/api/v1/config/network/selection", HTTP_METHOD_GET,
                           handle_network_selection_get, "获取网络选择配置") != 0) {
        return -1;
    }

    if (api_router_register("/api/v1/config/network/selection", HTTP_METHOD_POST,
                           handle_network_selection_post, "设置网络选择配置") != 0) {
        return -1;
    }

    if (api_router_register("/api/v1/config/network/ethernet", HTTP_METHOD_GET,
                           handle_network_ethernet_get, "获取以太网配置") != 0) {
        return -1;
    }

    if (api_router_register("/api/v1/config/network/ethernet", HTTP_METHOD_POST,
                           handle_network_ethernet_post, "设置以太网配置") != 0) {
        return -1;
    }

    // 注册呼叫中心配置API路由 - 按照设计方案对应0center.c
    if (api_router_register("/api/v1/config/center", HTTP_METHOD_GET,
                           handle_center_get, "获取呼叫中心配置") != 0) {
        return -1;
    }

    if (api_router_register("/api/v1/config/center", HTTP_METHOD_POST,
                           handle_center_post, "设置呼叫中心配置") != 0) {
        return -1;
    }

    // 注册网关配置API路由 - 按照设计方案对应0gateway.c
    if (api_router_register("/api/v1/config/gateway", HTTP_METHOD_GET,
                           handle_gateway_get, "获取网关配置") != 0) {
        return -1;
    }

    if (api_router_register("/api/v1/config/gateway", HTTP_METHOD_POST,
                           handle_gateway_post, "设置网关配置") != 0) {
        return -1;
    }

    printf("API routes registered successfully\n");
    return 0;
}

/**
 * @brief 主函数 - libmicrohttpd HTTP服务器模式
 */
int main(int argc, char *argv[]) {
    int port = 8080; // 默认端口

    // 解析命令行参数
    if (argc > 1) {
        port = atoi(argv[1]);
        if (port <= 0 || port > 65535) {
            printf("Invalid port number: %s\n", argv[1]);
            return -1;
        }
    }

    printf("WebCfg Modular System v1.0 - libmicrohttpd HTTP Server\n");
    printf("Starting on port %d...\n", port);

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 初始化配置管理器
    if (config_manager_init() != 0) {
        printf("Failed to initialize config manager\n");
        return -1;
    }

    // 初始化网络配置模块
    if (network_config_init() != 0) {
        printf("Failed to initialize network config\n");
        config_manager_cleanup();
        return -1;
    }

    // 初始化呼叫中心配置模块
    if (center_config_init() != 0) {
        printf("Failed to initialize center config\n");
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    // 初始化网关配置模块
    if (gateway_config_init() != 0) {
        printf("Failed to initialize gateway config\n");
        gateway_config_cleanup();
        center_config_cleanup();
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    // 初始化API路由系统
    if (api_router_init() != 0) {
        printf("Failed to initialize API router\n");
        gateway_config_cleanup();
        center_config_cleanup();
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    // 注册API路由
    if (register_api_routes() != 0) {
        printf("Failed to register API routes\n");
        api_router_cleanup();
        gateway_config_cleanup();
        center_config_cleanup();
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    // 配置HTTP服务器
    http_server_config_t server_config = {
        .port = port,
        .max_connections = 100,
        .timeout_seconds = 30,
        .document_root = "./web"
    };

    // 启动HTTP服务器
    if (http_server_start(&server_config) != 0) {
        printf("Failed to start HTTP server\n");
        api_router_cleanup();
        gateway_config_cleanup();
        center_config_cleanup();
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    printf("System initialized successfully.\n");
    printf("Registered modules: %d\n", config_manager_get_module_count());
    printf("HTTP server running on http://localhost:%d\n", port);
    printf("Press Ctrl+C to stop the server\n");

    // 主循环
    while (g_server_running) {
        sleep(1);
    }

    // 清理资源
    printf("Shutting down...\n");
    http_server_stop();
    api_router_cleanup();
    gateway_config_cleanup();
    center_config_cleanup();
    network_config_cleanup();
    config_manager_cleanup();

    printf("Server stopped successfully\n");
    return 0;
}