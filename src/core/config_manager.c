/**
 * @file config_manager.c
 * @brief 配置管理器实现
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#include "config_interface.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// 全局模块注册表
static config_module_t *g_registered_modules[MAX_CONFIG_MODULES];
static int g_module_count = 0;

/**
 * @brief 初始化配置管理器
 */
int config_manager_init(void) {
    memset(g_registered_modules, 0, sizeof(g_registered_modules));
    g_module_count = 0;
    return 0;
}

/**
 * @brief 清理配置管理器
 */
void config_manager_cleanup(void) {
    g_module_count = 0;
    memset(g_registered_modules, 0, sizeof(g_registered_modules));
}

/**
 * @brief 注册配置模块
 */
int config_manager_register(config_module_t *module) {
    if (!module || g_module_count >= MAX_CONFIG_MODULES) {
        return -1;
    }

    // 检查模块名是否已存在
    for (int i = 0; i < g_module_count; i++) {
        if (g_registered_modules[i] && 
            strcmp(g_registered_modules[i]->module_name, module->module_name) == 0) {
            return -1; // 模块已存在
        }
    }

    g_registered_modules[g_module_count] = module;
    g_module_count++;
    
    return 0;
}

/**
 * @brief 注销配置模块
 */
int config_manager_unregister(const char *module_name) {
    if (!module_name) {
        return -1;
    }

    for (int i = 0; i < g_module_count; i++) {
        if (g_registered_modules[i] && 
            strcmp(g_registered_modules[i]->module_name, module_name) == 0) {
            // 移动后续元素
            for (int j = i; j < g_module_count - 1; j++) {
                g_registered_modules[j] = g_registered_modules[j + 1];
            }
            g_registered_modules[g_module_count - 1] = NULL;
            g_module_count--;
            return 0;
        }
    }

    return -1; // 模块未找到
}

/**
 * @brief 查找配置模块
 */
config_module_t *config_manager_find(const char *module_name) {
    if (!module_name) {
        return NULL;
    }

    for (int i = 0; i < g_module_count; i++) {
        if (g_registered_modules[i] && 
            strcmp(g_registered_modules[i]->module_name, module_name) == 0) {
            return g_registered_modules[i];
        }
    }

    return NULL;
}

/**
 * @brief 获取所有注册的模块
 */
int config_manager_get_all_modules(config_module_t **modules, int max_count) {
    if (!modules || max_count <= 0) {
        return -1;
    }

    int count = (g_module_count < max_count) ? g_module_count : max_count;
    for (int i = 0; i < count; i++) {
        modules[i] = g_registered_modules[i];
    }

    return count;
}

/**
 * @brief 加载指定模块的配置
 */
int config_manager_load_config(const char *module_name, void *config) {
    config_module_t *module = config_manager_find(module_name);
    if (!module || !module->load_config) {
        return -1;
    }

    return module->load_config(config);
}

/**
 * @brief 保存指定模块的配置
 */
int config_manager_save_config(const char *module_name, const void *config) {
    config_module_t *module = config_manager_find(module_name);
    if (!module || !module->save_config) {
        return -1;
    }

    return module->save_config(config);
}

/**
 * @brief 验证指定模块的配置
 */
int config_manager_validate_config(const char *module_name, const void *config) {
    config_module_t *module = config_manager_find(module_name);
    if (!module || !module->validate_config) {
        return -1;
    }

    return module->validate_config(config);
}

/**
 * @brief 将配置转换为JSON
 */
int config_manager_config_to_json(const char *module_name, const void *config, cJSON **json) {
    config_module_t *module = config_manager_find(module_name);
    if (!module || !module->config_to_json) {
        return -1;
    }

    return module->config_to_json(config, json);
}

/**
 * @brief 将JSON转换为配置
 */
int config_manager_json_to_config(const char *module_name, const cJSON *json, void *config) {
    config_module_t *module = config_manager_find(module_name);
    if (!module || !module->json_to_config) {
        return -1;
    }

    return module->json_to_config(json, config);
}

/**
 * @brief 获取已注册模块数量
 */
int config_manager_get_module_count(void) {
    return g_module_count;
}

/**
 * @brief 列出所有已注册的模块名称
 */
int config_manager_list_modules(char **module_names, int max_count) {
    if (!module_names || max_count <= 0) {
        return -1;
    }

    int count = (g_module_count < max_count) ? g_module_count : max_count;
    for (int i = 0; i < count; i++) {
        if (g_registered_modules[i]) {
            module_names[i] = (char *)g_registered_modules[i]->module_name;
        }
    }

    return count;
}
