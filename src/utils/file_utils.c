/**
 * @file file_utils.c
 * @brief 文件操作工具模块实现
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#include "file_utils.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/stat.h>
#include <errno.h>
#include <libgen.h>

/**
 * @brief 读取配置文件
 */
int file_utils_read(const char *filepath, config_type_e type,
                   void *buffer, size_t size) {
    if (!filepath || !buffer || size == 0) {
        return -1;
    }

    if (type == CONFIG_TYPE_BINARY) {
        // 二进制配置文件读取 - 兼容旧项目ReadBinCfg
        return file_utils_read_binary(filepath, 0, size, buffer);
    } else if (type == CONFIG_TYPE_INI) {
        // INI格式配置文件读取 - 兼容旧项目INI读取方式
        return file_utils_read_ini(filepath, buffer, size);
    }

    return -1;
}

/**
 * @brief 写入配置文件
 */
int file_utils_write(const char *filepath, config_type_e type,
                    const void *buffer, size_t size) {
    if (!filepath || !buffer || size == 0) {
        return -1;
    }

    // 确保目录存在
    char *filepath_copy = strdup(filepath);
    char *dir = dirname(filepath_copy);
    file_utils_mkdir_recursive(dir);
    free(filepath_copy);

    if (type == CONFIG_TYPE_BINARY) {
        // 二进制配置文件写入 - 兼容旧项目WriteBinCfg
        return file_utils_write_binary(filepath, 0, size, buffer);
    } else if (type == CONFIG_TYPE_INI) {
        // INI格式配置文件写入 - 兼容旧项目INI写入方式
        return file_utils_write_ini(filepath, buffer, size);
    }

    return -1;
}

/**
 * @brief 备份配置文件
 */
int file_utils_backup(const char *filepath) {
    if (!filepath) {
        return -1;
    }

    if (!file_utils_exists(filepath)) {
        return 0; // 文件不存在，无需备份
    }

    char backup_path[512];
    snprintf(backup_path, sizeof(backup_path), "%s.backup", filepath);

    FILE *src = fopen(filepath, "rb");
    if (!src) {
        return -1;
    }

    FILE *dst = fopen(backup_path, "wb");
    if (!dst) {
        fclose(src);
        return -1;
    }

    char buffer[4096];
    size_t bytes;
    while ((bytes = fread(buffer, 1, sizeof(buffer), src)) > 0) {
        if (fwrite(buffer, 1, bytes, dst) != bytes) {
            fclose(src);
            fclose(dst);
            return -1;
        }
    }

    fclose(src);
    fclose(dst);
    return 0;
}

/**
 * @brief 恢复配置文件
 */
int file_utils_restore(const char *filepath) {
    if (!filepath) {
        return -1;
    }

    char backup_path[512];
    snprintf(backup_path, sizeof(backup_path), "%s.backup", filepath);

    if (!file_utils_exists(backup_path)) {
        return -1; // 备份文件不存在
    }

    FILE *src = fopen(backup_path, "rb");
    if (!src) {
        return -1;
    }

    FILE *dst = fopen(filepath, "wb");
    if (!dst) {
        fclose(src);
        return -1;
    }

    char buffer[4096];
    size_t bytes;
    while ((bytes = fread(buffer, 1, sizeof(buffer), src)) > 0) {
        if (fwrite(buffer, 1, bytes, dst) != bytes) {
            fclose(src);
            fclose(dst);
            return -1;
        }
    }

    fclose(src);
    fclose(dst);
    return 0;
}

/**
 * @brief 检查文件是否存在
 */
int file_utils_exists(const char *filepath) {
    if (!filepath) {
        return 0;
    }
    return access(filepath, F_OK) == 0 ? 1 : 0;
}

/**
 * @brief 获取文件大小
 */
long file_utils_size(const char *filepath) {
    if (!filepath) {
        return -1;
    }

    struct stat st;
    if (stat(filepath, &st) != 0) {
        return -1;
    }

    return st.st_size;
}

/**
 * @brief 创建目录（递归）
 */
int file_utils_mkdir_recursive(const char *dirpath) {
    if (!dirpath) {
        return -1;
    }

    char *path_copy = strdup(dirpath);
    char *p = path_copy;
    
    // 跳过根目录的斜杠
    if (*p == '/') {
        p++;
    }

    while (*p) {
        while (*p && *p != '/') {
            p++;
        }
        
        char saved = *p;
        *p = '\0';
        
        if (mkdir(path_copy, 0755) != 0 && errno != EEXIST) {
            free(path_copy);
            return -1;
        }
        
        *p = saved;
        if (*p) {
            p++;
        }
    }

    free(path_copy);
    return 0;
}

/**
 * @brief 读取二进制配置文件 - 兼容旧项目ReadBinCfg
 */
int file_utils_read_binary(const char *filepath, int offset, size_t size, void *buffer) {
    if (!filepath || !buffer || size == 0) {
        return -1;
    }

    FILE *fp = fopen(filepath, "rb");
    if (!fp) {
        return -1;
    }

    // 定位到指定偏移
    if (fseek(fp, offset, SEEK_SET) != 0) {
        fclose(fp);
        return -2;
    }

    // 读取数据
    size_t read_size = fread(buffer, 1, size, fp);
    fclose(fp);

    if (read_size != size) {
        return -3;
    }

    return (int)read_size;
}

/**
 * @brief 写入二进制配置文件 - 兼容旧项目WriteBinCfg
 */
int file_utils_write_binary(const char *filepath, int offset, size_t size, const void *buffer) {
    if (!filepath || !buffer || size == 0) {
        return -1;
    }

    FILE *fp = fopen(filepath, "r+b");
    if (!fp) {
        // 如果文件不存在，创建新文件
        fp = fopen(filepath, "w+b");
        if (!fp) {
            return -1;
        }
    }

    // 定位到指定偏移
    if (fseek(fp, offset, SEEK_SET) != 0) {
        fclose(fp);
        return -2;
    }

    // 写入数据
    size_t written_size = fwrite(buffer, 1, size, fp);
    fflush(fp);
    fclose(fp);

    if (written_size != size) {
        return -3;
    }

    return (int)written_size;
}

/**
 * @brief 读取INI格式配置文件 - 兼容旧项目INI读取方式
 */
int file_utils_read_ini(const char *filepath, void *buffer, size_t size) {
    if (!filepath || !buffer || size == 0) {
        return -1;
    }

    FILE *fp = fopen(filepath, "r");
    if (!fp) {
        return -1;
    }

    // 简单的全文件读取，实际项目中应该按行解析
    size_t read_size = fread(buffer, 1, size - 1, fp);
    fclose(fp);

    // 确保字符串结尾
    ((char*)buffer)[read_size] = '\0';

    return (int)read_size;
}

/**
 * @brief 写入INI格式配置文件 - 兼容旧项目INI写入方式
 */
int file_utils_write_ini(const char *filepath, const void *buffer, size_t size) {
    if (!filepath || !buffer || size == 0) {
        return -1;
    }

    FILE *fp = fopen(filepath, "w");
    if (!fp) {
        return -1;
    }

    size_t written_size = fwrite(buffer, 1, size, fp);
    fflush(fp);
    fclose(fp);

    if (written_size != size) {
        return -1;
    }

    return (int)written_size;
}
