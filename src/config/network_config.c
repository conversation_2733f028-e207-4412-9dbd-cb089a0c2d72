/**
 * @file network_config.c
 * @brief 网络配置模块实现 - 兼容旧项目网络配置
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#include "network_config.h"
#include "file_utils.h"
#include "network_utils.h"
#include "api_router.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>

// 全局配置模块注册表
static config_module_t g_network_selection_module;
static config_module_t g_network_ethernet_module;

/**
 * @brief 初始化网络配置模块
 */
int network_config_init(void) {
    // 初始化网络选择配置模块
    g_network_selection_module.module_name = "network_selection";
    g_network_selection_module.config_file_path = NETWORK_SETTING_CFG;
    g_network_selection_module.config_struct_size = sizeof(cfg_network_selection_t);
    g_network_selection_module.load_config = network_selection_load_config;
    g_network_selection_module.save_config = network_selection_save_config;
    g_network_selection_module.validate_config = network_selection_validate_config;
    g_network_selection_module.config_to_json = network_selection_config_to_json;
    g_network_selection_module.json_to_config = network_selection_json_to_config;

    // 初始化以太网配置模块
    g_network_ethernet_module.module_name = "network_ethernet";
    g_network_ethernet_module.config_file_path = ETHERNET_CFG;
    g_network_ethernet_module.config_struct_size = sizeof(cfg_ethernet_t);
    g_network_ethernet_module.load_config = network_ethernet_load_config;
    g_network_ethernet_module.save_config = network_ethernet_save_config;
    g_network_ethernet_module.validate_config = network_ethernet_validate_config;
    g_network_ethernet_module.config_to_json = network_ethernet_config_to_json;
    g_network_ethernet_module.json_to_config = network_ethernet_json_to_config;

    // 注册到配置管理器
    if (config_manager_register(&g_network_selection_module) != 0) {
        return -1;
    }
    
    if (config_manager_register(&g_network_ethernet_module) != 0) {
        return -1;
    }

    return 0;
}

/**
 * @brief 清理网络配置模块
 */
void network_config_cleanup(void) {
    // 清理工作（如果需要）
}

/**
 * @brief 获取网络选择配置 - GET /api/v1/config/network/selection
 */
int handle_network_selection_get(struct MHD_Connection *connection,
                                const char *url,
                                const char *method,
                                const char *upload_data,
                                size_t *upload_data_size,
                                void **con_cls) {
    (void)url; (void)method; (void)upload_data; (void)upload_data_size; (void)con_cls;

    cfg_network_selection_t config;
    cJSON *response_json = NULL;

    // 加载配置
    if (network_selection_load_config(&config) != 0) {
        // 发送错误响应
        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Failed to load network selection config");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 500, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    // 转换为JSON
    if (network_selection_config_to_json(&config, &response_json) != 0) {
        // 发送错误响应
        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Failed to convert config to JSON");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 500, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    // 发送成功响应
    char *json_str = cJSON_Print(response_json);
    struct MHD_Response *response = MHD_create_response_from_buffer(
        strlen(json_str), json_str, MHD_RESPMEM_MUST_COPY);
    MHD_add_response_header(response, "Content-Type", "application/json");
    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
    enum MHD_Result result = MHD_queue_response(connection, 200, response);

    free(json_str);
    cJSON_Delete(response_json);
    MHD_destroy_response(response);
    return (result == MHD_YES) ? 0 : -1;
}

/**
 * @brief 设置网络选择配置 - POST /api/v1/config/network/selection
 */
int handle_network_selection_post(struct MHD_Connection *connection,
                                 const char *url,
                                 const char *method,
                                 const char *upload_data,
                                 size_t *upload_data_size,
                                 void **con_cls) {
    (void)url; (void)method; (void)upload_data; (void)upload_data_size;

    // 获取连接状态和POST数据
    connection_state_t *conn_state = (connection_state_t *)*con_cls;
    if (!conn_state || !conn_state->post_data) {
        // 发送错误响应
        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "No JSON data provided");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 400, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    // 解析JSON
    cJSON *request_json = cJSON_Parse(conn_state->post_data);
    if (!request_json) {
        // 发送错误响应
        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Invalid JSON format");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 400, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    cfg_network_selection_t config;

    // JSON转配置结构
    if (network_selection_json_to_config(request_json, &config) != 0) {
        cJSON_Delete(request_json);

        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Failed to parse configuration");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 400, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    // 验证配置
    if (network_selection_validate_config(&config) != 0) {
        cJSON_Delete(request_json);

        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Configuration validation failed");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 400, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    // 保存配置
    if (network_selection_save_config(&config) != 0) {
        cJSON_Delete(request_json);

        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Failed to save configuration");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 500, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    cJSON_Delete(request_json);

    // 发送成功响应
    cJSON *success_json = cJSON_CreateObject();
    cJSON_AddStringToObject(success_json, "message", "Configuration saved successfully");
    char *success_str = cJSON_Print(success_json);

    struct MHD_Response *response = MHD_create_response_from_buffer(
        strlen(success_str), success_str, MHD_RESPMEM_MUST_COPY);
    MHD_add_response_header(response, "Content-Type", "application/json");
    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
    enum MHD_Result result = MHD_queue_response(connection, 200, response);

    free(success_str);
    cJSON_Delete(success_json);
    MHD_destroy_response(response);
    return (result == MHD_YES) ? 0 : -1;
}

/**
 * @brief 获取以太网配置 - GET /api/v1/config/network/ethernet
 */
int handle_network_ethernet_get(struct MHD_Connection *connection,
                               const char *url,
                               const char *method,
                               const char *upload_data,
                               size_t *upload_data_size,
                               void **con_cls) {
    (void)url; (void)method; (void)upload_data; (void)upload_data_size; (void)con_cls;

    cfg_ethernet_t config;
    cJSON *response_json = NULL;

    // 加载配置
    if (network_ethernet_load_config(&config) != 0) {
        // 发送错误响应
        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Failed to load ethernet config");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 500, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    // 转换为JSON
    if (network_ethernet_config_to_json(&config, &response_json) != 0) {
        // 发送错误响应
        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Failed to convert config to JSON");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 500, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    // 发送成功响应
    char *json_str = cJSON_Print(response_json);
    struct MHD_Response *response = MHD_create_response_from_buffer(
        strlen(json_str), json_str, MHD_RESPMEM_MUST_COPY);
    MHD_add_response_header(response, "Content-Type", "application/json");
    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
    enum MHD_Result result = MHD_queue_response(connection, 200, response);

    free(json_str);
    cJSON_Delete(response_json);
    MHD_destroy_response(response);
    return (result == MHD_YES) ? 0 : -1;
}

/**
 * @brief 设置以太网配置 - POST /api/v1/config/network/ethernet
 */
int handle_network_ethernet_post(struct MHD_Connection *connection,
                                const char *url,
                                const char *method,
                                const char *upload_data,
                                size_t *upload_data_size,
                                void **con_cls) {
    (void)url; (void)method; (void)upload_data; (void)upload_data_size;

    // 获取连接状态和POST数据
    connection_state_t *conn_state = (connection_state_t *)*con_cls;
    if (!conn_state || !conn_state->post_data) {
        // 发送错误响应
        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "No JSON data provided");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 400, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    // 解析JSON
    cJSON *request_json = cJSON_Parse(conn_state->post_data);
    if (!request_json) {
        // 发送错误响应
        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Invalid JSON format");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 400, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    cfg_ethernet_t config;

    // JSON转配置结构
    if (network_ethernet_json_to_config(request_json, &config) != 0) {
        cJSON_Delete(request_json);

        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Failed to parse ethernet configuration");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 400, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    // 验证配置
    if (network_ethernet_validate_config(&config) != 0) {
        cJSON_Delete(request_json);

        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Ethernet configuration validation failed");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 400, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    // 保存配置
    if (network_ethernet_save_config(&config) != 0) {
        cJSON_Delete(request_json);

        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Failed to save ethernet configuration");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 500, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    cJSON_Delete(request_json);

    // 发送成功响应
    cJSON *success_json = cJSON_CreateObject();
    cJSON_AddStringToObject(success_json, "message", "Ethernet configuration saved successfully");
    char *success_str = cJSON_Print(success_json);

    struct MHD_Response *response = MHD_create_response_from_buffer(
        strlen(success_str), success_str, MHD_RESPMEM_MUST_COPY);
    MHD_add_response_header(response, "Content-Type", "application/json");
    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
    enum MHD_Result result = MHD_queue_response(connection, 200, response);

    free(success_str);
    cJSON_Delete(success_json);
    MHD_destroy_response(response);
    return (result == MHD_YES) ? 0 : -1;
}

/**
 * @brief 网络选择配置加载函数 - 兼容旧项目read_netcs_config
 */
int network_selection_load_config(void *config) {
    cfg_network_selection_t *net_config = (cfg_network_selection_t *)config;
    char buffer[1024];
    int line_count = 0;
    
    if (!net_config) {
        return -1;
    }

    // 设置默认值
    network_selection_set_default(net_config);

    // 读取INI格式配置文件
    if (file_utils_read_ini(NETWORK_SETTING_CFG, buffer, sizeof(buffer)) < 0) {
        // 文件不存在，使用默认值
        return 0;
    }

    // 解析INI格式内容 - 兼容旧项目格式
    char *line_ptr = strtok(buffer, "\n");
    while (line_ptr && line_count < 5) {
        char key[32], value[64];
        if (sscanf(line_ptr, "%[^=]=%s", key, value) == 2) {
            switch (line_count) {
                case 0: // NET_TYPE
                    net_config->net_type = atoi(value);
                    break;
                case 1: // TEST_ETH1
                    strncpy(net_config->test_eth1, value, sizeof(net_config->test_eth1) - 1);
                    break;
                case 2: // TEST_ETH2
                    strncpy(net_config->test_eth2, value, sizeof(net_config->test_eth2) - 1);
                    break;
                case 3: // TEST_3G1
                    strncpy(net_config->test_3g1, value, sizeof(net_config->test_3g1) - 1);
                    break;
                case 4: // TEST_3G2
                    strncpy(net_config->test_3g2, value, sizeof(net_config->test_3g2) - 1);
                    break;
            }
        }
        line_ptr = strtok(NULL, "\n");
        line_count++;
    }

    return 0;
}

/**
 * @brief 网络选择配置保存函数 - 兼容旧项目write_netcs_config
 */
int network_selection_save_config(const void *config) {
    const cfg_network_selection_t *net_config = (const cfg_network_selection_t *)config;
    char buffer[1024];
    
    if (!net_config) {
        return -1;
    }

    // 构建INI格式内容 - 兼容旧项目格式
    snprintf(buffer, sizeof(buffer),
        "%s=%d\n"
        "%s=%s\n"
        "%s=%s\n"
        "%s=%s\n"
        "%s=%s\n",
        NET_KEY_TYPE, net_config->net_type,
        NET_KEY_ETH1, net_config->test_eth1,
        NET_KEY_ETH2, net_config->test_eth2,
        NET_KEY_3G1, net_config->test_3g1,
        NET_KEY_3G2, net_config->test_3g2);

    // 写入INI格式配置文件
    return file_utils_write_ini(NETWORK_SETTING_CFG, buffer, strlen(buffer));
}

/**
 * @brief 网络选择配置验证函数
 */
int network_selection_validate_config(const void *config) {
    const cfg_network_selection_t *net_config = (const cfg_network_selection_t *)config;
    
    if (!net_config) {
        return -1;
    }

    // 验证网络类型
    if (net_config->net_type < NETWORK_TYPE_ETH_ONLY || 
        net_config->net_type > NETWORK_TYPE_WWAN_ETH) {
        return -1;
    }

    // 验证测试地址格式
    if (ip_utils_validate_address(net_config->test_eth1) != 0 ||
        ip_utils_validate_address(net_config->test_eth2) != 0 ||
        ip_utils_validate_address(net_config->test_3g1) != 0 ||
        ip_utils_validate_address(net_config->test_3g2) != 0) {
        return -1;
    }

    return 0;
}

/**
 * @brief 网络选择配置转JSON
 */
int network_selection_config_to_json(const void *config, cJSON **json) {
    const cfg_network_selection_t *net_config = (const cfg_network_selection_t *)config;
    
    if (!net_config || !json) {
        return -1;
    }

    *json = cJSON_CreateObject();
    if (!*json) {
        return -1;
    }

    cJSON_AddNumberToObject(*json, "net_type", net_config->net_type);
    cJSON_AddStringToObject(*json, "test_eth1", net_config->test_eth1);
    cJSON_AddStringToObject(*json, "test_eth2", net_config->test_eth2);
    cJSON_AddStringToObject(*json, "test_3g1", net_config->test_3g1);
    cJSON_AddStringToObject(*json, "test_3g2", net_config->test_3g2);

    return 0;
}

/**
 * @brief JSON转网络选择配置
 */
int network_selection_json_to_config(const cJSON *json, void *config) {
    cfg_network_selection_t *net_config = (cfg_network_selection_t *)config;
    cJSON *item;
    
    if (!json || !net_config) {
        return -1;
    }

    // 设置默认值
    network_selection_set_default(net_config);

    // 解析JSON字段
    item = cJSON_GetObjectItem(json, "net_type");
    if (item && cJSON_IsNumber(item)) {
        net_config->net_type = item->valueint;
    }

    item = cJSON_GetObjectItem(json, "test_eth1");
    if (item && cJSON_IsString(item)) {
        strncpy(net_config->test_eth1, item->valuestring, sizeof(net_config->test_eth1) - 1);
    }

    item = cJSON_GetObjectItem(json, "test_eth2");
    if (item && cJSON_IsString(item)) {
        strncpy(net_config->test_eth2, item->valuestring, sizeof(net_config->test_eth2) - 1);
    }

    item = cJSON_GetObjectItem(json, "test_3g1");
    if (item && cJSON_IsString(item)) {
        strncpy(net_config->test_3g1, item->valuestring, sizeof(net_config->test_3g1) - 1);
    }

    item = cJSON_GetObjectItem(json, "test_3g2");
    if (item && cJSON_IsString(item)) {
        strncpy(net_config->test_3g2, item->valuestring, sizeof(net_config->test_3g2) - 1);
    }

    return 0;
}

/**
 * @brief 设置网络选择配置默认值 - 兼容旧项目set_default_value_netcs
 */
void network_selection_set_default(cfg_network_selection_t *config) {
    if (!config) {
        return;
    }

    config->net_type = NETWORK_TYPE_ETH_ONLY;
    strncpy(config->test_eth1, "************", sizeof(config->test_eth1) - 1);
    strncpy(config->test_eth2, "192.168.20.2", sizeof(config->test_eth2) - 1);
    strncpy(config->test_3g1, "223.5.5.5", sizeof(config->test_3g1) - 1);
    strncpy(config->test_3g2, "114.114.114.114", sizeof(config->test_3g2) - 1);
}

/**
 * @brief 以太网配置加载函数 - 兼容旧项目read_eth_config
 */
int network_ethernet_load_config(void *config) {
    cfg_ethernet_t *eth_config = (cfg_ethernet_t *)config;
    char buffer[1024];
    int line_count = 0;

    if (!eth_config) {
        return -1;
    }

    // 设置默认值
    network_ethernet_set_default(eth_config);

    // 读取INI格式配置文件
    if (file_utils_read_ini(ETHERNET_CFG, buffer, sizeof(buffer)) < 0) {
        // 文件不存在，使用默认值
        return 0;
    }

    // 解析INI格式内容 - 兼容旧项目格式
    char *line_ptr = strtok(buffer, "\n");
    while (line_ptr && line_count < 6) {
        char key[32], value[64];
        if (sscanf(line_ptr, "%[^=]=%s", key, value) == 2) {
            switch (line_count) {
                case 0: // IP
                    strncpy(eth_config->ip, value, sizeof(eth_config->ip) - 1);
                    break;
                case 1: // Mask
                    strncpy(eth_config->mask, value, sizeof(eth_config->mask) - 1);
                    break;
                case 2: // Gateway
                    strncpy(eth_config->gateway, value, sizeof(eth_config->gateway) - 1);
                    break;
                case 3: // DNS
                    strncpy(eth_config->dns, value, sizeof(eth_config->dns) - 1);
                    break;
                case 4: // MAC
                    strncpy(eth_config->mac, value, sizeof(eth_config->mac) - 1);
                    break;
                case 5: // WLAN
                    eth_config->wlan_enable = atoi(value);
                    break;
            }
        }
        line_ptr = strtok(NULL, "\n");
        line_count++;
    }

    return 0;
}

/**
 * @brief 以太网配置保存函数 - 兼容旧项目write_eth_config
 */
int network_ethernet_save_config(const void *config) {
    const cfg_ethernet_t *eth_config = (const cfg_ethernet_t *)config;
    char buffer[1024];

    if (!eth_config) {
        return -1;
    }

    // 构建INI格式内容 - 兼容旧项目格式
    snprintf(buffer, sizeof(buffer),
        "%s=%s\n"
        "%s=%s\n"
        "%s=%s\n"
        "%s=%s\n"
        "%s=%s\n"
        "%s=%d\n",
        ETH_KEY_IP, eth_config->ip,
        ETH_KEY_MASK, eth_config->mask,
        ETH_KEY_GATEWAY, eth_config->gateway,
        ETH_KEY_DNS, eth_config->dns,
        ETH_KEY_MAC, eth_config->mac,
        ETH_KEY_WLAN, eth_config->wlan_enable);

    // 写入INI格式配置文件
    return file_utils_write_ini(ETHERNET_CFG, buffer, strlen(buffer));
}

/**
 * @brief 以太网配置验证函数
 */
int network_ethernet_validate_config(const void *config) {
    const cfg_ethernet_t *eth_config = (const cfg_ethernet_t *)config;

    if (!eth_config) {
        return -1;
    }

    // 验证IP地址格式
    if (ip_utils_validate_address(eth_config->ip) != 0 ||
        ip_utils_validate_address(eth_config->mask) != 0 ||
        ip_utils_validate_address(eth_config->gateway) != 0 ||
        ip_utils_validate_address(eth_config->dns) != 0) {
        return -1;
    }

    // 验证MAC地址格式
    uint8_t mac_binary[6];
    if (mac_utils_string_to_binary(eth_config->mac, mac_binary) != 0) {
        return -1;
    }

    // 验证WLAN使能值
    if (eth_config->wlan_enable < 0 || eth_config->wlan_enable > 2) {
        return -1;
    }

    return 0;
}

/**
 * @brief 以太网配置转JSON
 */
int network_ethernet_config_to_json(const void *config, cJSON **json) {
    const cfg_ethernet_t *eth_config = (const cfg_ethernet_t *)config;

    if (!eth_config || !json) {
        return -1;
    }

    *json = cJSON_CreateObject();
    if (!*json) {
        return -1;
    }

    cJSON_AddStringToObject(*json, "ip", eth_config->ip);
    cJSON_AddStringToObject(*json, "mask", eth_config->mask);
    cJSON_AddStringToObject(*json, "gateway", eth_config->gateway);
    cJSON_AddStringToObject(*json, "dns", eth_config->dns);
    cJSON_AddStringToObject(*json, "mac", eth_config->mac);
    cJSON_AddNumberToObject(*json, "wlan_enable", eth_config->wlan_enable);

    return 0;
}

/**
 * @brief JSON转以太网配置
 */
int network_ethernet_json_to_config(const cJSON *json, void *config) {
    cfg_ethernet_t *eth_config = (cfg_ethernet_t *)config;
    cJSON *item;

    if (!json || !eth_config) {
        return -1;
    }

    // 设置默认值
    network_ethernet_set_default(eth_config);

    // 解析JSON字段
    item = cJSON_GetObjectItem(json, "ip");
    if (item && cJSON_IsString(item)) {
        strncpy(eth_config->ip, item->valuestring, sizeof(eth_config->ip) - 1);
    }

    item = cJSON_GetObjectItem(json, "mask");
    if (item && cJSON_IsString(item)) {
        strncpy(eth_config->mask, item->valuestring, sizeof(eth_config->mask) - 1);
    }

    item = cJSON_GetObjectItem(json, "gateway");
    if (item && cJSON_IsString(item)) {
        strncpy(eth_config->gateway, item->valuestring, sizeof(eth_config->gateway) - 1);
    }

    item = cJSON_GetObjectItem(json, "dns");
    if (item && cJSON_IsString(item)) {
        strncpy(eth_config->dns, item->valuestring, sizeof(eth_config->dns) - 1);
    }

    item = cJSON_GetObjectItem(json, "mac");
    if (item && cJSON_IsString(item)) {
        strncpy(eth_config->mac, item->valuestring, sizeof(eth_config->mac) - 1);
    }

    item = cJSON_GetObjectItem(json, "wlan_enable");
    if (item && cJSON_IsNumber(item)) {
        eth_config->wlan_enable = item->valueint;
    }

    return 0;
}

/**
 * @brief 设置以太网配置默认值 - 兼容旧项目set_default_value_boardnet
 */
void network_ethernet_set_default(cfg_ethernet_t *config) {
    if (!config) {
        return;
    }

    strncpy(config->ip, "************10", sizeof(config->ip) - 1);
    config->ip[sizeof(config->ip) - 1] = '\0';
    strncpy(config->mask, "*************", sizeof(config->mask) - 1);
    config->mask[sizeof(config->mask) - 1] = '\0';
    strncpy(config->gateway, "************", sizeof(config->gateway) - 1);
    config->gateway[sizeof(config->gateway) - 1] = '\0';
    strncpy(config->dns, "************", sizeof(config->dns) - 1);
    config->dns[sizeof(config->dns) - 1] = '\0';
    strncpy(config->mac, "08:90:c0:a8:01:2", sizeof(config->mac) - 1);
    config->mac[sizeof(config->mac) - 1] = '\0';
    config->wlan_enable = 0;
}
