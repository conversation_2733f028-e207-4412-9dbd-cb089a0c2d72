/**
 * @file gateway_config.c
 * @brief 网关配置管理模块实现 - 对应旧项目0gateway.c
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#include "gateway_config.h"
#include "config_interface.h"
#include "file_utils.h"
#include "network_utils.h"
#include "api_router.h"
#include <stdio.h>
#include <string.h>
#include <arpa/inet.h>

// 静态变量（按命名规范）
static config_module_t s_gateway_module;

/**
 * @brief 设置默认配置
 */
void gateway_config_set_default(cfg_gateway_t *config) {
    if (!config) return;
    
    memset(config, 0, sizeof(cfg_gateway_t));
    config->gateway_ip = inet_addr("***********");
    config->gateway_port = GATEWAY_DEFAULT_PORT;
    config->local_ip = inet_addr("*************");
    config->local_port = GATEWAY_DEFAULT_LOCAL_PORT;
    config->enable = 1;
    config->protocol_type = 0; // TCP
    config->timeout = GATEWAY_DEFAULT_TIMEOUT;
}

/**
 * @brief 加载网关配置
 */
int gateway_config_load(cfg_gateway_t *config) {
    if (!config) return -1;
    
    // 使用公共工具模块读取二进制配置文件
    if (file_utils_read_binary(GATEWAY_CONFIG_FILE, 0, sizeof(cfg_gateway_t), config) != 0) {
        printf("Warning: Failed to read gateway config, using defaults\n");
        gateway_config_set_default(config);
        return 0; // 使用默认配置不算错误
    }
    
    return 0;
}

/**
 * @brief 保存网关配置
 */
int gateway_config_save(const cfg_gateway_t *config) {
    if (!config) return -1;
    
    // 验证配置
    if (gateway_config_validate(config) != 0) {
        return -1;
    }
    
    // 使用公共工具模块写入二进制配置文件
    return file_utils_write_binary(GATEWAY_CONFIG_FILE, 0, sizeof(cfg_gateway_t), config);
}

/**
 * @brief 验证网关配置
 */
int gateway_config_validate(const cfg_gateway_t *config) {
    if (!config) return -1;
    
    // 验证端口范围（uint16_t最大值就是65535，无需检查上限）
    if (config->gateway_port == 0) {
        return -1;
    }
    
    if (config->local_port == 0) {
        return -1;
    }
    
    // 验证IP地址（简单检查非零）
    if (config->gateway_ip == 0 || config->local_ip == 0) {
        return -1;
    }
    
    // 验证协议类型
    if (config->protocol_type > 1) {
        return -1;
    }
    
    // 验证超时时间
    if (config->timeout == 0 || config->timeout > 3600) {
        return -1;
    }
    
    return 0;
}

/**
 * @brief 配置结构转JSON
 */
int gateway_config_to_json(const cfg_gateway_t *config, cJSON **json) {
    if (!config || !json) return -1;
    
    *json = cJSON_CreateObject();
    if (!*json) return -1;
    
    // 使用公共工具模块进行IP地址转换
    char ip_str[16];
    
    if (ip_utils_binary_to_string(config->gateway_ip, ip_str, sizeof(ip_str)) == 0) {
        cJSON_AddStringToObject(*json, "gateway_ip", ip_str);
    }
    
    if (ip_utils_binary_to_string(config->local_ip, ip_str, sizeof(ip_str)) == 0) {
        cJSON_AddStringToObject(*json, "local_ip", ip_str);
    }
    
    cJSON_AddNumberToObject(*json, "gateway_port", config->gateway_port);
    cJSON_AddNumberToObject(*json, "local_port", config->local_port);
    cJSON_AddBoolToObject(*json, "enable", config->enable ? 1 : 0);
    cJSON_AddNumberToObject(*json, "protocol_type", config->protocol_type);
    cJSON_AddNumberToObject(*json, "timeout", config->timeout);
    
    return 0;
}

/**
 * @brief JSON转配置结构
 */
int gateway_json_to_config(const cJSON *json, cfg_gateway_t *config) {
    if (!json || !config) return -1;
    
    // 先设置默认值
    gateway_config_set_default(config);
    
    // 解析JSON字段
    cJSON *item;
    
    item = cJSON_GetObjectItem(json, "gateway_ip");
    if (item && cJSON_IsString(item)) {
        if (ip_utils_string_to_binary(item->valuestring, &config->gateway_ip) != 0) {
            return -1;
        }
    }
    
    item = cJSON_GetObjectItem(json, "local_ip");
    if (item && cJSON_IsString(item)) {
        if (ip_utils_string_to_binary(item->valuestring, &config->local_ip) != 0) {
            return -1;
        }
    }
    
    item = cJSON_GetObjectItem(json, "gateway_port");
    if (item && cJSON_IsNumber(item)) {
        config->gateway_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "local_port");
    if (item && cJSON_IsNumber(item)) {
        config->local_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "enable");
    if (item && cJSON_IsBool(item)) {
        config->enable = cJSON_IsTrue(item) ? 1 : 0;
    }
    
    item = cJSON_GetObjectItem(json, "protocol_type");
    if (item && cJSON_IsNumber(item)) {
        config->protocol_type = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "timeout");
    if (item && cJSON_IsNumber(item)) {
        config->timeout = (uint16_t)item->valueint;
    }
    
    return 0;
}

/**
 * @brief 获取网关配置信息（API处理函数）
 */
int handle_gateway_get(struct MHD_Connection *connection, 
                      const char *url, 
                      const char *method,
                      const char *upload_data, 
                      size_t *upload_data_size,
                      void **con_cls) {
    (void)url; (void)method; (void)upload_data; (void)upload_data_size; (void)con_cls;
    
    cfg_gateway_t config;
    cJSON *json = NULL;
    
    // 加载配置
    if (gateway_config_load(&config) != 0) {
        api_response_t *response = api_response_create_error(500, "Failed to load gateway config");
        enum MHD_Result result = api_response_send(connection, response);
        api_response_free(response);
        return result;
    }
    
    // 转换为JSON
    if (gateway_config_to_json(&config, &json) != 0) {
        api_response_t *response = api_response_create_error(500, "Failed to convert config to JSON");
        enum MHD_Result result = api_response_send(connection, response);
        api_response_free(response);
        return result;
    }
    
    // 发送响应
    api_response_t *response = api_response_create_success(json);
    enum MHD_Result result = api_response_send(connection, response);
    api_response_free(response);
    cJSON_Delete(json);
    
    return result;
}

/**
 * @brief 设置网关配置信息（API处理函数）
 */
int handle_gateway_post(struct MHD_Connection *connection, 
                       const char *url, 
                       const char *method,
                       const char *upload_data, 
                       size_t *upload_data_size,
                       void **con_cls) {
    (void)url; (void)method;
    
    // 获取POST数据
    cJSON *json = api_request_get_json_data(connection, upload_data, upload_data_size, con_cls);
    if (!json) {
        api_response_t *response = api_response_create_error(400, "Invalid JSON data");
        enum MHD_Result result = api_response_send(connection, response);
        api_response_free(response);
        return result;
    }
    
    cfg_gateway_t config;
    
    // JSON转配置结构
    if (gateway_json_to_config(json, &config) != 0) {
        cJSON_Delete(json);
        api_response_t *response = api_response_create_error(400, "Invalid config data");
        enum MHD_Result result = api_response_send(connection, response);
        api_response_free(response);
        return result;
    }
    
    // 保存配置
    if (gateway_config_save(&config) != 0) {
        cJSON_Delete(json);
        api_response_t *response = api_response_create_error(500, "Failed to save config");
        enum MHD_Result result = api_response_send(connection, response);
        api_response_free(response);
        return result;
    }
    
    cJSON_Delete(json);
    
    // 返回成功响应
    cJSON *success_json = cJSON_CreateObject();
    cJSON_AddStringToObject(success_json, "message", "Gateway config saved successfully");
    
    api_response_t *response = api_response_create_success(success_json);
    enum MHD_Result result = api_response_send(connection, response);
    api_response_free(response);
    cJSON_Delete(success_json);
    
    return result;
}

// 配置模块接口实现（按命名规范）
static int gateway_module_load(void *config) {
    return gateway_config_load((cfg_gateway_t *)config);
}

static int gateway_module_save(const void *config) {
    return gateway_config_save((const cfg_gateway_t *)config);
}

static int gateway_module_validate(const void *config) {
    return gateway_config_validate((const cfg_gateway_t *)config);
}

static int gateway_module_to_json(const void *config, cJSON **json) {
    return gateway_config_to_json((const cfg_gateway_t *)config, json);
}

static int gateway_module_from_json(const cJSON *json, void *config) {
    return gateway_json_to_config(json, (cfg_gateway_t *)config);
}

/**
 * @brief 初始化网关配置模块
 */
int gateway_config_init(void) {
    // 初始化配置模块结构
    s_gateway_module.module_name = "gateway";
    s_gateway_module.config_file_path = GATEWAY_CONFIG_FILE;
    s_gateway_module.config_struct_size = sizeof(cfg_gateway_t);
    s_gateway_module.load_config = gateway_module_load;
    s_gateway_module.save_config = gateway_module_save;
    s_gateway_module.validate_config = gateway_module_validate;
    s_gateway_module.config_to_json = gateway_module_to_json;
    s_gateway_module.json_to_config = gateway_module_from_json;

    // 注册到配置管理器
    if (config_manager_register(&s_gateway_module) != 0) {
        printf("Failed to register gateway config module\n");
        return -1;
    }

    printf("Gateway config module initialized\n");
    return 0;
}

/**
 * @brief 清理网关配置模块
 */
void gateway_config_cleanup(void) {
    // 清理模块资源（如果有的话）
    printf("Gateway config module cleaned up\n");
}
