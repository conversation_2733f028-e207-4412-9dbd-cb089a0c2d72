# 第三方库管理器 - 主入口
# 模块化的第三方库构建和管理系统

# 引入子模块
include(${CMAKE_CURRENT_LIST_DIR}/modules/ThirdPartyConfig.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/modules/ThirdPartyUtils.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/modules/AutotoolsBuilder.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/modules/CMakeBuilder.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/modules/MakefileBuilder.cmake)

# 主要的第三方库处理函数
function(handle_third_party_libraries)
    # 验证环境
    if(NOT DEFINED WEBCFG_ROOT_DIR)
        message(FATAL_ERROR "WEBCFG_ROOT_DIR 未定义")
    endif()
    if(NOT DEFINED WEBCFG_CACHE_DIR)
        message(FATAL_ERROR "WEBCFG_CACHE_DIR 未定义")
    endif()
    
    message(STATUS "开始处理第三方库...")
    
    # 如果指定了只构建第三方库
    if(ONLY_BUILD_THIRD_PARTY)
        message(STATUS "仅构建第三方库模式")
        _build_all_libraries()
        return()
    endif()
    
    # 正常模式：构建并设置库
    _build_and_setup_libraries()
    
    message(STATUS "第三方库处理完成")
endfunction()

# 构建所有库（仅构建模式）
function(_build_all_libraries)
    foreach(LIB_NAME ${THIRD_PARTY_LIBS})
        _build_single_library(${LIB_NAME})
    endforeach()
endfunction()

# 构建并设置库（正常模式）
function(_build_and_setup_libraries)
    set(SETUP_LIBRARIES "")
    
    foreach(LIB_NAME ${THIRD_PARTY_LIBS})
        # 构建库
        _build_single_library(${LIB_NAME})
        
        # 设置库目标
        _setup_library_target(${LIB_NAME})
        
        list(APPEND SETUP_LIBRARIES ${LIB_NAME})
    endforeach()
    
    list(LENGTH SETUP_LIBRARIES LIB_COUNT)
    message(STATUS "已设置 ${LIB_COUNT} 个第三方库: ${SETUP_LIBRARIES}")
endfunction()

# 构建单个库
function(_build_single_library LIB_NAME)
    set(LIB_SOURCE_DIR "${WEBCFG_ROOT_DIR}/third_party/${LIB_NAME}")
    
    # 检查源码是否存在
    check_library_source(${LIB_NAME} ${WEBCFG_ROOT_DIR} SOURCE_EXISTS)
    if(NOT SOURCE_EXISTS)
        message(WARNING "跳过不存在的第三方库: ${LIB_NAME}")
        return()
    endif()
    
    # 验证库配置
    validate_library_config(${LIB_NAME})
    
    # 检查缓存
    tp_check_cache(${LIB_NAME} ${LIB_SOURCE_DIR} ${WEBCFG_CACHE_DIR} CACHE_EXISTS)
    
    if(CACHE_EXISTS)
        message(STATUS "使用缓存的第三方库: ${LIB_NAME}")
        return()
    endif()
    
    # 根据构建类型选择构建方式
    get_library_build_type(${LIB_NAME} BUILD_TYPE)
    
    if(${BUILD_TYPE} STREQUAL "autotools")
        autotools_build_library(${LIB_NAME} ${LIB_SOURCE_DIR} ${WEBCFG_CACHE_DIR})
    elseif(${BUILD_TYPE} STREQUAL "cmake")
        cmake_build_library(${LIB_NAME} ${LIB_SOURCE_DIR} ${WEBCFG_CACHE_DIR})
    elseif(${BUILD_TYPE} STREQUAL "makefile")
        makefile_build_library(${LIB_NAME} ${LIB_SOURCE_DIR} ${WEBCFG_CACHE_DIR})
    else()
        message(FATAL_ERROR "不支持的构建类型: ${BUILD_TYPE}")
    endif()
endfunction()

# 设置库目标
function(_setup_library_target LIB_NAME)
    tp_create_imported_target(${LIB_NAME} ${WEBCFG_CACHE_DIR})
endfunction()

# 显示第三方库信息
function(show_third_party_info)
    message(STATUS "第三方库信息:")
    message(STATUS "==============")
    
    foreach(LIB_NAME ${THIRD_PARTY_LIBS})
        tp_show_library_info(${LIB_NAME} ${WEBCFG_CACHE_DIR})
    endforeach()
endfunction()

# 清理第三方库缓存
function(clean_third_party_cache)
    tp_clean_cache(${WEBCFG_CACHE_DIR})
endfunction()

# 显示配置信息
function(show_third_party_config)
    show_libraries_config(${WEBCFG_ROOT_DIR})
endfunction() 