# 第三方库通用工具模块
# 提供缓存管理、哈希计算等通用功能

# 获取源码的MD5哈希
function(tp_get_source_hash SOURCE_DIR OUTPUT_VAR)
    # 查找所有相关文件
    file(GLOB_RECURSE SOURCE_FILES 
        "${SOURCE_DIR}/*.c" 
        "${SOURCE_DIR}/*.h" 
        "${SOURCE_DIR}/*.cpp"
        "${SOURCE_DIR}/*.hpp"
        "${SOURCE_DIR}/*.in"
        "${SOURCE_DIR}/configure*"
        "${SOURCE_DIR}/Makefile*"
        "${SOURCE_DIR}/*.m4"
        "${SOURCE_DIR}/CMakeLists.txt"
    )
    
    set(HASH_INPUT "")
    foreach(FILE ${SOURCE_FILES})
        if(EXISTS ${FILE})
            file(READ ${FILE} FILE_CONTENT)
            string(APPEND HASH_INPUT ${FILE_CONTENT})
        endif()
    endforeach()
    
    # 添加编译器和平台信息
    string(APPEND HASH_INPUT "${TARGET_PLATFORM}_${CMAKE_BUILD_TYPE}")
    if(CMAKE_TOOLCHAIN_FILE)
        string(APPEND HASH_INPUT "${CMAKE_TOOLCHAIN_FILE}")
    endif()
    if(CMAKE_C_COMPILER)
        string(APPEND HASH_INPUT "${CMAKE_C_COMPILER}")
    endif()
    
    string(MD5 HASH_VALUE "${HASH_INPUT}")
    set(${OUTPUT_VAR} ${HASH_VALUE} PARENT_SCOPE)
endfunction()

# 检查缓存是否有效
function(tp_check_cache LIB_NAME SOURCE_DIR CACHE_DIR OUTPUT_VAR)
    tp_get_source_hash(${SOURCE_DIR} SOURCE_HASH)
    
    set(CACHE_LIB_DIR "${CACHE_DIR}/${LIB_NAME}")
    set(HASH_FILE "${CACHE_LIB_DIR}/.source_hash")
    set(LIB_FILE "${CACHE_LIB_DIR}/lib/lib${LIB_NAME}.a")
    
    set(CACHE_VALID FALSE)
    
    if(EXISTS ${HASH_FILE} AND EXISTS ${LIB_FILE})
        file(READ ${HASH_FILE} CACHED_HASH)
        string(STRIP ${CACHED_HASH} CACHED_HASH)
        if("${CACHED_HASH}" STREQUAL "${SOURCE_HASH}")
            set(CACHE_VALID TRUE)
        endif()
    endif()
    
    set(${OUTPUT_VAR} ${CACHE_VALID} PARENT_SCOPE)
endfunction()

# 保存缓存哈希
function(tp_save_cache_hash LIB_NAME SOURCE_DIR CACHE_DIR)
    tp_get_source_hash(${SOURCE_DIR} SOURCE_HASH)
    set(CACHE_LIB_DIR "${CACHE_DIR}/${LIB_NAME}")
    file(WRITE "${CACHE_LIB_DIR}/.source_hash" ${SOURCE_HASH})
endfunction()

# 获取并行编译数量
function(tp_get_parallel_jobs OUTPUT_VAR)
    include(ProcessorCount)
    ProcessorCount(N_CORES)
    if(N_CORES EQUAL 0)
        set(N_CORES 4)
    endif()
    set(${OUTPUT_VAR} ${N_CORES} PARENT_SCOPE)
endfunction()

# 创建导入目标
function(tp_create_imported_target LIB_NAME CACHE_DIR)
    set(CACHE_LIB_DIR "${CACHE_DIR}/${LIB_NAME}")
    set(LIB_FILE "${CACHE_LIB_DIR}/lib/lib${LIB_NAME}.a")
    set(INCLUDE_DIR "${CACHE_LIB_DIR}/include")
    
    if(NOT EXISTS ${LIB_FILE})
        message(FATAL_ERROR "库文件不存在: ${LIB_FILE}")
    endif()
    
    # 创建导入目标
    add_library(${LIB_NAME} STATIC IMPORTED GLOBAL)
    set_target_properties(${LIB_NAME} PROPERTIES
        IMPORTED_LOCATION ${LIB_FILE}
    )
    
    # 设置包含目录
    if(EXISTS ${INCLUDE_DIR})
        set_target_properties(${LIB_NAME} PROPERTIES
            INTERFACE_INCLUDE_DIRECTORIES ${INCLUDE_DIR}
        )
        message(STATUS "添加包含目录: ${INCLUDE_DIR}")
    endif()
    
    # 设置全局变量
    set(${LIB_NAME}_FOUND TRUE CACHE BOOL "Found ${LIB_NAME}")
    set(${LIB_NAME}_LIBRARIES ${LIB_FILE} CACHE STRING "${LIB_NAME} libraries")
    set(${LIB_NAME}_INCLUDE_DIRS ${INCLUDE_DIR} CACHE STRING "${LIB_NAME} include directories")
endfunction()

# 显示库信息
function(tp_show_library_info LIB_NAME CACHE_DIR)
    set(CACHE_LIB_DIR "${CACHE_DIR}/${LIB_NAME}")
    set(LIB_FILE "${CACHE_LIB_DIR}/lib/lib${LIB_NAME}.a")
    
    if(EXISTS ${LIB_FILE})
        file(SIZE ${LIB_FILE} LIB_SIZE)
        math(EXPR LIB_SIZE_KB "${LIB_SIZE} / 1024")
        message(STATUS "  ${LIB_NAME}: ${LIB_SIZE_KB} KB")
    else()
        message(STATUS "  ${LIB_NAME}: 未构建")
    endif()
endfunction()

# 清理缓存
function(tp_clean_cache CACHE_DIR)
    if(EXISTS ${CACHE_DIR})
        message(STATUS "清理第三方库缓存: ${CACHE_DIR}")
        file(REMOVE_RECURSE ${CACHE_DIR})
    endif()
endfunction() 