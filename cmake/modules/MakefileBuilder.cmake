# Makefile构建器模块
# 用于构建使用传统Makefile的第三方库

# 使用Makefile构建库
function(makefile_build_library LIB_NAME SOURCE_DIR CACHE_DIR)
    message(STATUS "使用Makefile构建库: ${LIB_NAME}")
    
    set(LIB_CACHE_DIR "${CACHE_DIR}/${LIB_NAME}")
    set(LIB_LIB_DIR "${LIB_CACHE_DIR}/lib")
    set(LIB_INCLUDE_DIR "${LIB_CACHE_DIR}/include")
    
    # 创建缓存目录
    file(MAKE_DIRECTORY ${LIB_CACHE_DIR})
    file(MAKE_DIRECTORY ${LIB_LIB_DIR})
    file(MAKE_DIRECTORY ${LIB_INCLUDE_DIR})
    
    # 检查是否需要清理
    if(EXISTS "${SOURCE_DIR}/Makefile")
        execute_process(
            COMMAND make clean
            WORKING_DIRECTORY ${SOURCE_DIR}
            OUTPUT_QUIET
            ERROR_QUIET
        )
    endif()
    
    # 设置构建环境变量
    set(ENV{CC} ${CMAKE_C_COMPILER})
    set(ENV{CXX} ${CMAKE_CXX_COMPILER})
    set(ENV{AR} ${CMAKE_AR})
    if(CMAKE_C_FLAGS)
        set(ENV{CFLAGS} ${CMAKE_C_FLAGS})
    endif()
    
    # 执行make构建
    execute_process(
        COMMAND make
        WORKING_DIRECTORY ${SOURCE_DIR}
        RESULT_VARIABLE MAKE_RESULT
        OUTPUT_VARIABLE MAKE_OUTPUT
        ERROR_VARIABLE MAKE_ERROR
    )
    
    if(NOT MAKE_RESULT EQUAL 0)
        message(FATAL_ERROR "Makefile构建失败: ${LIB_NAME}\n输出: ${MAKE_OUTPUT}\n错误: ${MAKE_ERROR}")
    endif()
    
    # 根据库的特定需求进行安装
    if(${LIB_NAME} STREQUAL "cgic")
        _install_cgic_library(${SOURCE_DIR} ${LIB_LIB_DIR} ${LIB_INCLUDE_DIR})
    else()
        message(WARNING "未知的Makefile库: ${LIB_NAME}, 使用默认安装方式")
        _install_makefile_library_default(${SOURCE_DIR} ${LIB_LIB_DIR} ${LIB_INCLUDE_DIR})
    endif()
    
    message(STATUS "Makefile构建库完成: ${LIB_NAME}")
endfunction()

# 安装cgic库
function(_install_cgic_library SOURCE_DIR LIB_DIR INCLUDE_DIR)
    # 复制静态库文件
    if(EXISTS "${SOURCE_DIR}/libcgic.a")
        file(COPY "${SOURCE_DIR}/libcgic.a" DESTINATION ${LIB_DIR})
    else()
        message(FATAL_ERROR "cgic库文件不存在: ${SOURCE_DIR}/libcgic.a")
    endif()
    
    # 复制头文件
    if(EXISTS "${SOURCE_DIR}/cgic.h")
        file(COPY "${SOURCE_DIR}/cgic.h" DESTINATION ${INCLUDE_DIR})
    else()
        message(FATAL_ERROR "cgic头文件不存在: ${SOURCE_DIR}/cgic.h")
    endif()
    
    message(STATUS "cgic库安装完成")
endfunction()

# 默认的Makefile库安装方式
function(_install_makefile_library_default SOURCE_DIR LIB_DIR INCLUDE_DIR)
    # 查找静态库文件
    file(GLOB STATIC_LIBS "${SOURCE_DIR}/*.a")
    if(STATIC_LIBS)
        file(COPY ${STATIC_LIBS} DESTINATION ${LIB_DIR})
        message(STATUS "复制静态库文件: ${STATIC_LIBS}")
    endif()
    
    # 查找头文件
    file(GLOB HEADER_FILES "${SOURCE_DIR}/*.h")
    if(HEADER_FILES)
        file(COPY ${HEADER_FILES} DESTINATION ${INCLUDE_DIR})
        message(STATUS "复制头文件: ${HEADER_FILES}")
    endif()
endfunction() 