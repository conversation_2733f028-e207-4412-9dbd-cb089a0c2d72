# 第三方库配置模块
# 定义每个第三方库的构建方式和配置

# 第三方库列表定义
set(THIRD_PARTY_LIBS
    cjson
    microhttpd
)

# 第三方库构建类型映射
# autotools: 使用 autotools 构建系统
# cmake: 使用 CMake 构建系统
# makefile: 使用传统 Makefile 构建系统
function(get_library_build_type LIB_NAME OUTPUT_VAR)
    if(${LIB_NAME} STREQUAL "microhttpd")
        set(${OUTPUT_VAR} "autotools" PARENT_SCOPE)
    elseif(${LIB_NAME} STREQUAL "cjson")
        set(${OUTPUT_VAR} "cmake" PARENT_SCOPE)

    elseif(${LIB_NAME} STREQUAL "zlib")
        set(${OUTPUT_VAR} "cmake" PARENT_SCOPE)
    elseif(${LIB_NAME} STREQUAL "openssl")
        set(${OUTPUT_VAR} "autotools" PARENT_SCOPE)
    elseif(${LIB_NAME} STREQUAL "libcurl")
        set(${OUTPUT_VAR} "autotools" PARENT_SCOPE)
    else()
        # 默认使用 cmake
        set(${OUTPUT_VAR} "cmake" PARENT_SCOPE)
    endif()
endfunction()

# 检查库是否存在
function(check_library_source LIB_NAME ROOT_DIR OUTPUT_VAR)
    set(LIB_SOURCE_DIR "${ROOT_DIR}/third_party/${LIB_NAME}")
    
    if(NOT EXISTS ${LIB_SOURCE_DIR})
        set(${OUTPUT_VAR} FALSE PARENT_SCOPE)
        return()
    endif()
    
    # 根据构建类型检查必要文件
    get_library_build_type(${LIB_NAME} BUILD_TYPE)
    
    if(${BUILD_TYPE} STREQUAL "autotools")
        # 检查 autotools 相关文件
        if(EXISTS "${LIB_SOURCE_DIR}/configure" OR 
           EXISTS "${LIB_SOURCE_DIR}/autogen.sh" OR
           EXISTS "${LIB_SOURCE_DIR}/configure.ac" OR
           EXISTS "${LIB_SOURCE_DIR}/configure.in")
            set(${OUTPUT_VAR} TRUE PARENT_SCOPE)
        else()
            set(${OUTPUT_VAR} FALSE PARENT_SCOPE)
        endif()
    elseif(${BUILD_TYPE} STREQUAL "cmake")
        # 检查 CMakeLists.txt
        if(EXISTS "${LIB_SOURCE_DIR}/CMakeLists.txt")
            set(${OUTPUT_VAR} TRUE PARENT_SCOPE)
        else()
            set(${OUTPUT_VAR} FALSE PARENT_SCOPE)
        endif()
    elseif(${BUILD_TYPE} STREQUAL "makefile")
        # 检查 Makefile
        if(EXISTS "${LIB_SOURCE_DIR}/Makefile" OR EXISTS "${LIB_SOURCE_DIR}/makefile")
            set(${OUTPUT_VAR} TRUE PARENT_SCOPE)
        else()
            set(${OUTPUT_VAR} FALSE PARENT_SCOPE)
        endif()
    else()
        set(${OUTPUT_VAR} FALSE PARENT_SCOPE)
    endif()
endfunction()

# 获取库的特殊依赖
function(get_library_dependencies LIB_NAME OUTPUT_VAR)
    set(DEPS "")
    
    if(${LIB_NAME} STREQUAL "microhttpd")
        # microhttpd 可能需要一些系统库
        list(APPEND DEPS "pthread")
    elseif(${LIB_NAME} STREQUAL "libcurl")
        # libcurl 可能依赖于其他库
        list(APPEND DEPS "zlib")
    endif()
    
    set(${OUTPUT_VAR} ${DEPS} PARENT_SCOPE)
endfunction()

# 获取库的链接要求
function(get_library_link_requirements LIB_NAME OUTPUT_VAR)
    set(LINK_LIBS "")
    
    if(${LIB_NAME} STREQUAL "microhttpd")
        list(APPEND LINK_LIBS "pthread")
    elseif(${LIB_NAME} STREQUAL "cgic")
        # cgic 通常不需要额外链接库
    elseif(${LIB_NAME} STREQUAL "cjson")
        list(APPEND LINK_LIBS "m")  # 数学库
    endif()
    
    set(${OUTPUT_VAR} ${LINK_LIBS} PARENT_SCOPE)
endfunction()

# 验证库配置
function(validate_library_config LIB_NAME)
    get_library_build_type(${LIB_NAME} BUILD_TYPE)
    
    if(NOT BUILD_TYPE STREQUAL "autotools" AND 
       NOT BUILD_TYPE STREQUAL "cmake" AND 
       NOT BUILD_TYPE STREQUAL "makefile")
        message(FATAL_ERROR "不支持的构建类型: ${BUILD_TYPE} (库: ${LIB_NAME})")
    endif()
    
    message(STATUS "库 ${LIB_NAME} 配置: 构建类型=${BUILD_TYPE}")
endfunction()

# 显示所有库的配置信息
function(show_libraries_config ROOT_DIR)
    message(STATUS "第三方库配置信息:")
    message(STATUS "==================")
    
    foreach(LIB_NAME ${THIRD_PARTY_LIBS})
        get_library_build_type(${LIB_NAME} BUILD_TYPE)
        check_library_source(${LIB_NAME} ${ROOT_DIR} SOURCE_EXISTS)
        get_library_dependencies(${LIB_NAME} DEPS)
        get_library_link_requirements(${LIB_NAME} LINK_LIBS)
        
        message(STATUS "库名: ${LIB_NAME}")
        message(STATUS "  构建类型: ${BUILD_TYPE}")
        message(STATUS "  源码存在: ${SOURCE_EXISTS}")
        if(DEPS)
            message(STATUS "  依赖: ${DEPS}")
        endif()
        if(LINK_LIBS)
            message(STATUS "  链接库: ${LINK_LIBS}")
        endif()
        message(STATUS "")
    endforeach()
endfunction() 