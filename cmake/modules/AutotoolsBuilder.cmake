# Autotools 构建器模块
# 专门处理基于 autotools 的第三方库构建

include(${CMAKE_CURRENT_LIST_DIR}/ThirdPartyUtils.cmake)

# 使用 autotools 构建第三方库
function(autotools_build_library LIB_NAME SOURCE_DIR CACHE_DIR)
    message(STATUS "开始使用 autotools 构建第三方库: ${LIB_NAME}")
    
    set(CACHE_LIB_DIR "${CACHE_DIR}/${LIB_NAME}")
    set(BUILD_DIR "${CACHE_LIB_DIR}/build")
    
    # 创建目录
    file(MAKE_DIRECTORY ${CACHE_LIB_DIR})
    file(MAKE_DIRECTORY ${BUILD_DIR})
    
    # 检查并运行 autogen.sh
    _autotools_prepare_configure(${SOURCE_DIR})
    
    # 配置构建
    _autotools_configure(${LIB_NAME} ${SOURCE_DIR} ${CACHE_LIB_DIR} ${BUILD_DIR})
    
    # 编译
    _autotools_build(${LIB_NAME} ${BUILD_DIR})
    
    # 安装
    _autotools_install(${LIB_NAME} ${BUILD_DIR})
    
    # 保存缓存
    tp_save_cache_hash(${LIB_NAME} ${SOURCE_DIR} ${CACHE_DIR})
    
    message(STATUS "第三方库构建完成: ${LIB_NAME}")
endfunction()

# 准备 configure 脚本
function(_autotools_prepare_configure SOURCE_DIR)
    set(CONFIGURE_SCRIPT "${SOURCE_DIR}/configure")
    
    if(NOT EXISTS ${CONFIGURE_SCRIPT})
        set(AUTOGEN_SCRIPT "${SOURCE_DIR}/autogen.sh")
        if(EXISTS ${AUTOGEN_SCRIPT})
            message(STATUS "运行 autogen.sh 生成 configure 脚本")
            execute_process(
                COMMAND bash ${AUTOGEN_SCRIPT}
                WORKING_DIRECTORY ${SOURCE_DIR}
                RESULT_VARIABLE AUTOGEN_RESULT
                OUTPUT_VARIABLE AUTOGEN_OUTPUT
                ERROR_VARIABLE AUTOGEN_ERROR
            )
            if(NOT AUTOGEN_RESULT EQUAL 0)
                message(FATAL_ERROR "autogen.sh 失败: ${AUTOGEN_ERROR}")
            endif()
        else()
            message(FATAL_ERROR "无法找到 configure 脚本或 autogen.sh")
        endif()
    endif()
endfunction()

# 配置构建选项
function(_autotools_configure LIB_NAME SOURCE_DIR CACHE_LIB_DIR BUILD_DIR)
    set(CONFIGURE_SCRIPT "${SOURCE_DIR}/configure")
    
    # 基本配置参数
    set(CONFIGURE_ARGS
        "--prefix=${CACHE_LIB_DIR}"
        "--enable-static"
        "--disable-shared"
        "--with-pic"
    )
    
    # 库特定配置
    _autotools_add_library_specific_args(${LIB_NAME} CONFIGURE_ARGS)
    
    # 交叉编译支持
    _autotools_add_cross_compile_args(CONFIGURE_ARGS)
    
    # 设置环境变量
    _autotools_prepare_environment(BUILD_ENV)
    
    # 运行配置
    message(STATUS "配置 ${LIB_NAME}...")
    execute_process(
        COMMAND ${CMAKE_COMMAND} -E env ${BUILD_ENV}
                ${CONFIGURE_SCRIPT} ${CONFIGURE_ARGS}
        WORKING_DIRECTORY ${BUILD_DIR}
        RESULT_VARIABLE CONFIGURE_RESULT
        OUTPUT_VARIABLE CONFIGURE_OUTPUT
        ERROR_VARIABLE CONFIGURE_ERROR
    )
    
    if(NOT CONFIGURE_RESULT EQUAL 0)
        message(FATAL_ERROR "配置第三方库失败: ${LIB_NAME}\n"
                "输出: ${CONFIGURE_OUTPUT}\n"
                "错误: ${CONFIGURE_ERROR}")
    endif()
endfunction()

# 添加库特定的配置参数
function(_autotools_add_library_specific_args LIB_NAME CONFIGURE_ARGS_VAR)
    set(ARGS ${${CONFIGURE_ARGS_VAR}})
    
    if(${LIB_NAME} STREQUAL "microhttpd")
        list(APPEND ARGS
            "--disable-doc"
            "--disable-examples"
            "--disable-curl"
            "--disable-https"
        )
    elseif(${LIB_NAME} STREQUAL "libcurl")
        list(APPEND ARGS
            "--disable-ldap"
            "--disable-rtsp"
            "--without-ssl"
        )
    endif()
    
    set(${CONFIGURE_ARGS_VAR} ${ARGS} PARENT_SCOPE)
endfunction()

# 添加交叉编译参数
function(_autotools_add_cross_compile_args CONFIGURE_ARGS_VAR)
    set(ARGS ${${CONFIGURE_ARGS_VAR}})
    
    if(CMAKE_CROSSCOMPILING)
        if(CMAKE_SYSTEM_PROCESSOR MATCHES "arm")
            list(APPEND ARGS "--host=arm-linux-gnueabihf")
        elseif(CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64")
            list(APPEND ARGS "--host=aarch64-linux-gnu")
        elseif(CMAKE_SYSTEM_PROCESSOR MATCHES "mips")
            list(APPEND ARGS "--host=mips-linux-gnu")
        endif()
    endif()
    
    set(${CONFIGURE_ARGS_VAR} ${ARGS} PARENT_SCOPE)
endfunction()

# 准备编译环境变量
function(_autotools_prepare_environment BUILD_ENV_VAR)
    set(ENV_VARS)
    
    if(CMAKE_C_COMPILER)
        list(APPEND ENV_VARS "CC=${CMAKE_C_COMPILER}")
    endif()
    
    if(CMAKE_CXX_COMPILER)
        list(APPEND ENV_VARS "CXX=${CMAKE_CXX_COMPILER}")
    endif()
    
    # 设置 CFLAGS
    set(CFLAGS "-fPIC")
    if(CMAKE_C_FLAGS)
        string(APPEND CFLAGS " ${CMAKE_C_FLAGS}")
    endif()
    list(APPEND ENV_VARS "CFLAGS=${CFLAGS}")
    
    # 设置 CXXFLAGS
    set(CXXFLAGS "-fPIC")
    if(CMAKE_CXX_FLAGS)
        string(APPEND CXXFLAGS " ${CMAKE_CXX_FLAGS}")
    endif()
    list(APPEND ENV_VARS "CXXFLAGS=${CXXFLAGS}")
    
    set(${BUILD_ENV_VAR} ${ENV_VARS} PARENT_SCOPE)
endfunction()

# 编译库
function(_autotools_build LIB_NAME BUILD_DIR)
    message(STATUS "编译 ${LIB_NAME}...")
    
    tp_get_parallel_jobs(N_CORES)
    
    execute_process(
        COMMAND make -j${N_CORES}
        WORKING_DIRECTORY ${BUILD_DIR}
        RESULT_VARIABLE BUILD_RESULT
        OUTPUT_VARIABLE BUILD_OUTPUT
        ERROR_VARIABLE BUILD_ERROR
    )
    
    if(NOT BUILD_RESULT EQUAL 0)
        message(FATAL_ERROR "构建第三方库失败: ${LIB_NAME}\n"
                "输出: ${BUILD_OUTPUT}\n"
                "错误: ${BUILD_ERROR}")
    endif()
endfunction()

# 安装库
function(_autotools_install LIB_NAME BUILD_DIR)
    message(STATUS "安装 ${LIB_NAME}...")
    
    execute_process(
        COMMAND make install
        WORKING_DIRECTORY ${BUILD_DIR}
        RESULT_VARIABLE INSTALL_RESULT
        OUTPUT_VARIABLE INSTALL_OUTPUT
        ERROR_VARIABLE INSTALL_ERROR
    )
    
    if(NOT INSTALL_RESULT EQUAL 0)
        message(FATAL_ERROR "安装第三方库失败: ${LIB_NAME}\n"
                "输出: ${INSTALL_OUTPUT}\n"
                "错误: ${INSTALL_ERROR}")
    endif()
endfunction() 