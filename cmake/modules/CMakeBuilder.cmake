# CMake 构建器模块
# 专门处理基于 CMake 的第三方库构建

include(${CMAKE_CURRENT_LIST_DIR}/ThirdPartyUtils.cmake)

# 使用 CMake 构建第三方库
function(cmake_build_library LIB_NAME SOURCE_DIR CACHE_DIR)
    message(STATUS "开始使用 CMake 构建第三方库: ${LIB_NAME}")
    
    set(CACHE_LIB_DIR "${CACHE_DIR}/${LIB_NAME}")
    set(BUILD_DIR "${CACHE_LIB_DIR}/build")
    
    # 创建目录
    file(MAKE_DIRECTORY ${CACHE_LIB_DIR})
    file(MAKE_DIRECTORY ${BUILD_DIR})
    
    # 配置构建
    _cmake_configure(${LIB_NAME} ${SOURCE_DIR} ${CACHE_LIB_DIR} ${BUILD_DIR})
    
    # 编译
    _cmake_build(${LIB_NAME} ${BUILD_DIR})
    
    # 安装
    _cmake_install(${LIB_NAME} ${BUILD_DIR})
    
    # 保存缓存
    tp_save_cache_hash(${LIB_NAME} ${SOURCE_DIR} ${CACHE_DIR})
    
    message(STATUS "第三方库构建完成: ${LIB_NAME}")
endfunction()

# 配置 CMake 构建
function(_cmake_configure LIB_NAME SOURCE_DIR CACHE_LIB_DIR BUILD_DIR)
    # 基本配置参数
    set(CMAKE_CONFIG_ARGS
        -DCMAKE_BUILD_TYPE=${CMAKE_BUILD_TYPE}
        -DCMAKE_INSTALL_PREFIX=${CACHE_LIB_DIR}
        -DCMAKE_POSITION_INDEPENDENT_CODE=ON
        -DBUILD_SHARED_LIBS=OFF
    )
    
    # 传递工具链文件
    if(CMAKE_TOOLCHAIN_FILE)
        list(APPEND CMAKE_CONFIG_ARGS -DCMAKE_TOOLCHAIN_FILE=${CMAKE_TOOLCHAIN_FILE})
    endif()
    
    # 传递编译器设置
    if(CMAKE_C_COMPILER)
        list(APPEND CMAKE_CONFIG_ARGS -DCMAKE_C_COMPILER=${CMAKE_C_COMPILER})
    endif()
    if(CMAKE_CXX_COMPILER)
        list(APPEND CMAKE_CONFIG_ARGS -DCMAKE_CXX_COMPILER=${CMAKE_CXX_COMPILER})
    endif()
    
    # 添加库特定配置
    _cmake_add_library_specific_args(${LIB_NAME} CMAKE_CONFIG_ARGS)
    
    # 运行 CMake 配置
    execute_process(
        COMMAND ${CMAKE_COMMAND} ${CMAKE_CONFIG_ARGS} ${SOURCE_DIR}
        WORKING_DIRECTORY ${BUILD_DIR}
        RESULT_VARIABLE CMAKE_RESULT
        OUTPUT_VARIABLE CMAKE_OUTPUT
        ERROR_VARIABLE CMAKE_ERROR
    )
    
    if(NOT CMAKE_RESULT EQUAL 0)
        message(FATAL_ERROR "配置第三方库失败: ${LIB_NAME}\n"
                "输出: ${CMAKE_OUTPUT}\n"
                "错误: ${CMAKE_ERROR}")
    endif()
endfunction()

# 添加库特定的配置参数
function(_cmake_add_library_specific_args LIB_NAME CMAKE_CONFIG_ARGS_VAR)
    set(ARGS ${${CMAKE_CONFIG_ARGS_VAR}})
    
    if(${LIB_NAME} STREQUAL "cjson")
        list(APPEND ARGS 
            -DENABLE_CJSON_TEST=OFF
            -DENABLE_CJSON_UTILS=OFF
            -DCJSON_BUILD_SHARED_LIBS=OFF
        )
    elseif(${LIB_NAME} STREQUAL "cgic")
        # cgic 通常没有特殊选项
    elseif(${LIB_NAME} STREQUAL "zlib")
        list(APPEND ARGS
            -DZLIB_BUILD_EXAMPLES=OFF
        )
    elseif(${LIB_NAME} STREQUAL "openssl")
        list(APPEND ARGS
            -DOPENSSL_NO_ASM=ON
            -DBUILD_TESTING=OFF
        )
    endif()
    
    set(${CMAKE_CONFIG_ARGS_VAR} ${ARGS} PARENT_SCOPE)
endfunction()

# 编译库
function(_cmake_build LIB_NAME BUILD_DIR)
    message(STATUS "编译 ${LIB_NAME}...")
    
    tp_get_parallel_jobs(N_CORES)
    
    execute_process(
        COMMAND ${CMAKE_COMMAND} --build . --parallel ${N_CORES}
        WORKING_DIRECTORY ${BUILD_DIR}
        RESULT_VARIABLE BUILD_RESULT
        OUTPUT_VARIABLE BUILD_OUTPUT
        ERROR_VARIABLE BUILD_ERROR
    )
    
    if(NOT BUILD_RESULT EQUAL 0)
        message(FATAL_ERROR "构建第三方库失败: ${LIB_NAME}\n"
                "输出: ${BUILD_OUTPUT}\n"
                "错误: ${BUILD_ERROR}")
    endif()
endfunction()

# 安装库
function(_cmake_install LIB_NAME BUILD_DIR)
    message(STATUS "安装 ${LIB_NAME}...")
    
    execute_process(
        COMMAND ${CMAKE_COMMAND} --build . --target install
        WORKING_DIRECTORY ${BUILD_DIR}
        RESULT_VARIABLE INSTALL_RESULT
        OUTPUT_VARIABLE INSTALL_OUTPUT
        ERROR_VARIABLE INSTALL_ERROR
    )
    
    if(NOT INSTALL_RESULT EQUAL 0)
        message(FATAL_ERROR "安装第三方库失败: ${LIB_NAME}\n"
                "输出: ${INSTALL_OUTPUT}\n"
                "错误: ${INSTALL_ERROR}")
    endif()
endfunction() 