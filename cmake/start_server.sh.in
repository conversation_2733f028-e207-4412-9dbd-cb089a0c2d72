#!/bin/bash
# WebCfg 服务器启动脚本
# 平台: @TARGET_PLATFORM@
# 版本: @PROJECT_VERSION@

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SERVER_BIN="${SCRIPT_DIR}/webcfg"
WEB_ROOT="${SCRIPT_DIR}/../../../common/web"
DEFAULT_PORT=80

# 检查可执行文件是否存在
if [ ! -f "$SERVER_BIN" ]; then
    echo "错误: 找不到服务器程序 $SERVER_BIN"
    exit 1
fi

# 检查Web资源目录
if [ ! -d "$WEB_ROOT" ]; then
    echo "警告: Web资源目录不存在 $WEB_ROOT"
    WEB_ROOT="./web"
fi

# 解析命令行参数
PORT=$DEFAULT_PORT
DAEMON=0

while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -w|--web-root)
            WEB_ROOT="$2"
            shift 2
            ;;
        -d|--daemon)
            DAEMON=1
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  -p, --port PORT      设置监听端口 (默认: $DEFAULT_PORT)"
            echo "  -w, --web-root DIR   设置Web资源目录 (默认: $WEB_ROOT)"
            echo "  -d, --daemon         以守护进程模式运行"
            echo "  -h, --help           显示此帮助信息"
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            echo "使用 -h 查看帮助信息"
            exit 1
            ;;
    esac
done

# 构建启动命令
START_CMD="$SERVER_BIN -p $PORT -w $WEB_ROOT"

echo "=== WebCfg 服务器启动 ==="
echo "平台: @TARGET_PLATFORM@"
echo "端口: $PORT"
echo "Web根目录: $WEB_ROOT"
echo "========================="

# 启动服务器
if [ $DAEMON -eq 1 ]; then
    echo "以守护进程模式启动..."
    nohup $START_CMD > /dev/null 2>&1 &
    echo "服务器已启动，PID: $!"
    echo "访问地址: http://localhost:$PORT"
else
    echo "启动服务器..."
    echo "按 Ctrl+C 停止服务器"
    exec $START_CMD
fi 