webcfg 平台信息
==================

构建信息:
  项目名称: @PROJECT_NAME@
  版本: @PROJECT_VERSION@
  目标平台: @TARGET_PLATFORM@
  构建类型: @CMAKE_BUILD_TYPE@
  构建时间: @BUILD_TIMESTAMP@

编译器信息:
  C编译器: @CMAKE_C_COMPILER@
  编译器版本: @CMAKE_C_COMPILER_VERSION@
  工具链文件: @CMAKE_TOOLCHAIN_FILE@

第三方库:
  cJSON: 静态库
  microhttpd: 静态库
  cgic: 静态库

目录结构:
  可执行文件: bin/webcfg-server
  库文件: lib/
  配置文件: (可自定义)
  日志文件: (可自定义)

运行说明:
  1. 确保具有执行权限
  2. 检查网络端口是否可用
  3. 根据需要修改配置文件
  4. 运行: ./bin/webcfg-server

技术支持:
  项目主页: (待定义)
  文档地址: (待定义) 