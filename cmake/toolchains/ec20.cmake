# EC20交叉编译工具链配置
set(CMAKE_SYSTEM_NAME Linux)
set(CMAKE_SYSTEM_PROCESSOR arm)

set(SDK_CROSSTOOL_PATH "/disk/platform/EC20CEFAG/ql-ol-sdk/ql-ol-crosstool")
# 交叉编译器路径
set(CROSS_COMPILE_PREFIX "${SDK_CROSSTOOL_PATH}/sysroots/x86_64-oesdk-linux/usr/bin/arm-oe-linux-gnueabi/arm-oe-linux-gnueabi-")
set(CMAKE_C_COMPILER ${CROSS_COMPILE_PREFIX}gcc)
set(CMAKE_CXX_COMPILER ${CROSS_COMPILE_PREFIX}g++)
set(CMAKE_ASM_COMPILER ${CROSS_COMPILE_PREFIX}gcc)

# 工具链工具
set(CMAKE_AR ${CROSS_COMPILE_PREFIX}ar)
set(CMAKE_RANLIB ${CROSS_COMPILE_PREFIX}ranlib)
set(CMAKE_STRIP ${CROSS_COMPILE_PREFIX}strip)

# 系统根目录
set(CMAKE_SYSROOT "${SDK_CROSSTOOL_PATH}/sysroots/armv7a-vfp-neon-oe-linux-gnueabi")
set(CMAKE_FIND_ROOT_PATH ${CMAKE_SYSROOT})

# 搜索路径配置
set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_PACKAGE ONLY)

# 修复VFP寄存器参数问题 - 使用软浮点ABI替代硬浮点
# 原因：工具链的库可能是用软浮点编译的，与硬浮点不兼容
set(CMAKE_C_FLAGS_INIT "-mcpu=cortex-a7 -mfpu=neon-vfpv4 -mfloat-abi=softfp -mthumb")
set(CMAKE_CXX_FLAGS_INIT "-mcpu=cortex-a7 -mfpu=neon-vfpv4 -mfloat-abi=softfp -mthumb")

# 添加必要的链接器选项以确保ABI一致性
set(CMAKE_EXE_LINKER_FLAGS_INIT "-Wl,--gc-sections -mfloat-abi=softfp")
set(CMAKE_SHARED_LINKER_FLAGS_INIT "-Wl,--gc-sections -mfloat-abi=softfp")

# 平台标识
add_definitions(-DTARGET_PLATFORM_EC20=1)

# 禁用一些可能导致兼容性问题的特性
set(CMAKE_TRY_COMPILE_TARGET_TYPE STATIC_LIBRARY) 